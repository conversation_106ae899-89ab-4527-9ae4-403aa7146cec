#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团签名RPC服务HTTP服务器 - 简化版

使用DrissionPage提供美团H5guard签名生成服务的简化版本。
服务启动后会自动进行初始化，无需手动调用初始化接口。

API接口:
- POST /api/signature/init - 手动初始化签名服务（可选，服务会自动初始化）
- POST /api/signature/sign - 生成签名
- GET /api/signature/status - 获取服务状态

使用方法:
1. 安装依赖: pip install flask DrissionPage
2. 启动服务: python meituan_signature_server.py
3. 等待自动初始化完成
4. 访问 http://localhost:5000

特性:
- 自动初始化: 服务启动后自动初始化签名功能
- 后台初始化: 初始化在后台线程进行，不阻塞服务启动
- 状态监控: 可通过状态接口查看初始化进度

作者: AI Assistant
日期: 2025-01-02
"""

import time
import logging
import threading
from typing import Dict, Any
from flask import Flask, request, jsonify
from DrissionPage import ChromiumPage, ChromiumOptions

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

class MeituanSignatureService:
    """美团签名服务"""
    
    def __init__(self):
        self.driver = None
        self.initialized = False
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def setup_browser(self) -> bool:
        """设置浏览器"""
        try:
            if self.driver:
                return True
                
            # 配置浏览器选项
            options = ChromiumOptions()
            options.headless(False)  # 显示浏览器窗口以便调试
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            
            # 创建浏览器实例
            self.driver = ChromiumPage(addr_or_opts=options)
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def init_service(self) -> Dict[str, Any]:
        """初始化签名服务"""
        try:
            if self.initialized:
                return {"success": True, "message": "服务已初始化"}
                
            # 设置浏览器
            if not self.setup_browser():
                return {"success": False, "error": "浏览器初始化失败"}
                
            # 访问美团页面
            self.logger.info("正在访问美团页面...")
            self.driver.get("https://sqt.meituan.com")
            time.sleep(5)  # 等待页面加载
            
            # 检查H5guard
            h5guard_check = self.driver.run_js("""
                return {
                    loaded: !!(window.H5guard),
                    hasSign: !!(window.H5guard && window.H5guard.sign)
                };
            """)
            
            if not h5guard_check.get('loaded') or not h5guard_check.get('hasSign'):
                return {"success": False, "error": "H5guard未正确加载"}
                
            # 加载RPC脚本
            self.logger.info("加载签名RPC服务...")
            try:
                with open('meituan_signature_rpc.js', 'r', encoding='utf-8') as f:
                    rpc_script = f.read()
                self.driver.run_js(rpc_script)
            except FileNotFoundError:
                return {"success": False, "error": "RPC脚本文件未找到"}
            
            # 初始化签名API
            self.driver.run_js("""
                window.initSignatureAPI = async function() {
                    try {
                        const success = await MeituanSignatureAPI.init({ debug: true });
                        window.signatureAPIReady = success;
                        return success;
                    } catch (error) {
                        window.signatureAPIError = error.message;
                        return false;
                    }
                };
                window.initSignatureAPI();
            """)
            
            # 等待初始化完成
            time.sleep(3)
            
            # 检查初始化结果
            api_ready = self.driver.run_js("return window.signatureAPIReady;")
            if api_ready:
                self.initialized = True
                self.logger.info("签名服务初始化成功")
                return {"success": True, "message": "签名服务初始化成功"}
            else:
                error = self.driver.run_js("return window.signatureAPIError || 'Unknown error';")
                return {"success": False, "error": f"签名API初始化失败: {error}"}
                
        except Exception as e:
            self.logger.error(f"初始化服务失败: {e}")
            return {"success": False, "error": str(e)}
    
    def generate_signature(self, url: str, method: str, data: Any = None) -> Dict[str, Any]:
        """生成签名"""
        try:
            if not self.initialized:
                return {"success": False, "error": "服务未初始化"}

            with self.lock:
                # 调用签名生成
                self.driver.run_js("""
                    window.generateSignature = async function(params) {
                        try {
                            const result = await MeituanSignatureAPI.sign(params);
                            window.signatureResult = result;
                            return result;
                        } catch (error) {
                            window.signatureResult = { success: false, error: error.message };
                            return window.signatureResult;
                        }
                    };

                    const params = arguments[0];
                    window.generateSignature(params);
                """, {
                    'url': url,
                    'method': method,
                    'data': data
                })

                # 等待结果
                time.sleep(2)

                # 获取结果
                result = self.driver.run_js("return window.signatureResult;")

                if result:
                    # 优化返回结构，避免重复数据
                    return self._format_signature_result(result, url, method, data)
                else:
                    return {"success": False, "error": "签名生成超时"}
                
        except Exception as e:
            self.logger.error(f"生成签名失败: {e}")
            return {"success": False, "error": str(e)}

    def _format_signature_result(self, result: Dict[str, Any], url: str, method: str, data: Any) -> Dict[str, Any]:
        """格式化签名结果，避免重复数据"""
        try:
            # 检查结果是否成功
            if not result.get('success', True):
                return result

            # 提取签名信息
            signature_data = result.get('signature', {})
            mtgsig = signature_data.get('mtgsig') or signature_data.get('headers', {}).get('mtgsig')

            # 如果没有找到mtgsig，尝试从其他位置获取
            if not mtgsig:
                mtgsig = result.get('mtgsig')

            # 构造清晰的返回结构
            formatted_result = {
                "success": True,
                "timestamp": int(time.time() * 1000),
                "request": {
                    "url": url,
                    "method": method,
                    "data": data
                },
                "signature": {
                    "mtgsig": mtgsig
                }
            }

            # 如果需要调试信息，可以添加原始结果
            if self.logger.level <= 10:  # DEBUG level
                formatted_result["debug"] = {
                    "raw_result": result
                }

            return formatted_result

        except Exception as e:
            self.logger.error(f"格式化签名结果失败: {e}")
            # 如果格式化失败，返回原始结果
            return result
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        try:
            if not self.initialized:
                return {"initialized": False, "error": "服务未初始化"}
                
            status = self.driver.run_js("return MeituanSignatureAPI.getStatus();")
            return {
                "initialized": self.initialized,
                "browser_active": bool(self.driver),
                "service_status": status
            }
            
        except Exception as e:
            return {"initialized": self.initialized, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            self.driver = None
        self.initialized = False


# 创建服务实例
signature_service = MeituanSignatureService()

@app.route('/')
def index():
    """首页"""
    return jsonify({
        "service": "美团签名API服务 - DrissionPage版",
        "version": "1.0.0",
        "endpoints": {
            "init": "POST /api/signature/init",
            "sign": "POST /api/signature/sign",
            "status": "GET /api/signature/status"
        }
    })

@app.route('/api/signature/init', methods=['POST'])
def init_signature_service():
    """初始化签名服务"""
    result = signature_service.init_service()
    return jsonify(result)

@app.route('/api/signature/sign', methods=['POST'])
def generate_signature():
    """生成签名"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "请求数据为空"}), 400
            
        url = data.get('url')
        method = data.get('method')
        request_data = data.get('data')
        
        if not url or not method:
            return jsonify({"success": False, "error": "缺少必要参数"}), 400
            
        result = signature_service.generate_signature(url, method, request_data)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/signature/status', methods=['GET'])
def get_signature_status():
    """获取签名服务状态"""
    result = signature_service.get_status()
    return jsonify(result)

@app.errorhandler(404)
def not_found(_):
    """404错误处理"""
    return jsonify({"error": "接口不存在"}), 404

@app.errorhandler(500)
def internal_error(_):
    """500错误处理"""
    return jsonify({"error": "服务器内部错误"}), 500

def auto_initialize_service():
    """自动初始化签名服务"""
    print("正在自动初始化签名服务...")

    def init_worker():
        """在后台线程中执行初始化"""
        try:
            result = signature_service.init_service()
            if result.get('success'):
                print("✓ 签名服务自动初始化成功")
            else:
                print(f"✗ 签名服务自动初始化失败: {result.get('error', '未知错误')}")
                print("  您可以稍后手动调用 POST /api/signature/init 进行初始化")
        except Exception as e:
            print(f"✗ 签名服务自动初始化异常: {e}")
            print("  您可以稍后手动调用 POST /api/signature/init 进行初始化")

    # 在后台线程中执行初始化，避免阻塞主线程
    init_thread = threading.Thread(target=init_worker, daemon=True)
    init_thread.start()

def cleanup_on_exit():
    """退出时清理资源"""
    signature_service.cleanup()

if __name__ == '__main__':
    import atexit
    atexit.register(cleanup_on_exit)

    print("=" * 50)
    print("美团签名API服务启动中... (DrissionPage版)")
    print("API文档: http://localhost:5000")
    print("=" * 50)

    # 启动自动初始化
    auto_initialize_service()

    try:
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    finally:
        cleanup_on_exit()
