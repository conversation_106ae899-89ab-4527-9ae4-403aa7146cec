#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HTTP API测试

测试DrissionPage版本的美团签名HTTP服务器。

使用方法:
python test/test_http_api_simple.py

作者: AI Assistant
日期: 2025-01-02
"""

import requests
import json
import time

def test_http_api():
    """测试HTTP API"""
    base_url = "http://localhost:5000"
    
    print("🧪 开始测试美团签名HTTP API...")
    print("=" * 50)
    
    try:
        # 1. 测试服务器连接
        print("🔍 测试服务器连接...")
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器连接成功: {data['service']}")
        else:
            print("❌ 服务器连接失败")
            return False
        
        # 2. 初始化服务
        print("\n🚀 初始化签名服务...")
        response = requests.post(f"{base_url}/api/signature/init")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 服务初始化成功: {data['message']}")
            else:
                print(f"❌ 服务初始化失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 初始化请求失败: {response.status_code}")
            return False
        
        # 等待初始化完成
        time.sleep(5)
        
        # 3. 检查服务状态
        print("\n📊 检查服务状态...")
        response = requests.get(f"{base_url}/api/signature/status")
        if response.status_code == 200:
            data = response.json()
            if data.get('initialized'):
                print("✅ 服务状态正常")
            else:
                print(f"❌ 服务未初始化: {data}")
                return False
        else:
            print(f"❌ 状态检查失败: {response.status_code}")
            return False
        
        # 4. 测试签名生成
        print("\n🔐 测试签名生成...")
        test_data = {
            "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
            "method": "POST",
            "data": {
                "mobile": "17139144117",
                "countrycode": "86"
            }
        }
        
        response = requests.post(
            f"{base_url}/api/signature/sign",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                signature = data.get('signature', {})
                mtgsig = signature.get('mtgsig')
                if mtgsig:
                    print("✅ 签名生成成功")
                    print(f"   URL: {signature.get('url')}")
                    print(f"   Method: {signature.get('method')}")
                    print(f"   mtgsig: {mtgsig[:50]}...")
                    
                    # 验证签名格式
                    try:
                        sig_obj = json.loads(mtgsig)
                        if 'a1' in sig_obj and 'a2' in sig_obj and 'a3' in sig_obj:
                            print("✅ 签名格式验证通过")
                            return True
                        else:
                            print("❌ 签名格式不正确")
                            return False
                    except json.JSONDecodeError:
                        print("❌ 签名不是有效的JSON")
                        return False
                else:
                    print("❌ 签名为空")
                    return False
            else:
                print(f"❌ 签名生成失败: {data.get('error')}")
                return False
        else:
            print(f"❌ 签名请求失败: {response.status_code}")
            return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请先启动服务器: python meituan_signature_server_simple.py")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False


def main():
    """主函数"""
    print("美团签名HTTP API简化测试")
    print("=" * 50)
    
    success = test_http_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 HTTP API测试成功！DrissionPage版本的服务器工作正常")
        print("💡 现在可以在其他程序中调用这个API了")
    else:
        print("⚠️  HTTP API测试失败，请检查服务器状态")


if __name__ == "__main__":
    main()
