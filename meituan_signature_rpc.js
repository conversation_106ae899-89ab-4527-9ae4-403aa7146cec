/**
 * 美团H5guard签名生成RPC服务
 * 基于美团H5guard签名机制，提供签名生成的RPC接口
 * 
 * 使用方法：
 * 1. 在美团页面环境中加载此脚本
 * 2. 调用 MeituanSignatureRPC.generateSignature() 方法
 * 3. 获取签名结果用于API调用
 */

class MeituanSignatureRPC {
    constructor(options = {}) {
        this.debug = options.debug || false;
        this.timeout = options.timeout || 10000;
        this.initialized = false;
        
        // 初始化
        this.init();
    }

    /**
     * 初始化签名服务
     */
    async init() {
        try {
            await this.waitForH5guard();
            this.initialized = true;
            this.log('美团签名服务初始化成功');
        } catch (error) {
            this.log('初始化失败:', error.message);
            throw error;
        }
    }

    /**
     * 等待H5guard加载
     * @param {number} timeout 超时时间(毫秒)
     * @returns {Promise<Object>} H5guard对象
     */
    waitForH5guard(timeout = this.timeout) {
        return new Promise((resolve, reject) => {
            if (window.H5guard && typeof window.H5guard.sign === 'function') {
                resolve(window.H5guard);
                return;
            }

            const startTime = Date.now();
            const checkInterval = setInterval(() => {
                if (window.H5guard && typeof window.H5guard.sign === 'function') {
                    clearInterval(checkInterval);
                    resolve(window.H5guard);
                } else if (Date.now() - startTime > timeout) {
                    clearInterval(checkInterval);
                    reject(new Error('H5guard加载超时，请确保在美团页面环境中运行'));
                }
            }, 100);
        });
    }

    /**
     * 生成美团API签名
     * @param {Object} params 签名参数
     * @param {string} params.url API地址
     * @param {string} params.method HTTP方法 (GET/POST/PUT/DELETE)
     * @param {Object|string} params.data 请求数据
     * @returns {Promise<Object>} 签名结果
     */
    async generateSignature(params) {
        if (!this.initialized) {
            throw new Error('签名服务未初始化，请先调用init()方法');
        }

        // 参数验证
        this.validateParams(params);

        try {
            const { url, method, data } = params;
            
            this.log('开始生成签名:', { url, method, data });

            // 调用H5guard.sign生成签名
            const signedRequest = await window.H5guard.sign({
                url: url,
                method: method.toUpperCase(),
                data: data
            });

            this.log('签名生成成功:', signedRequest);

            // 返回标准化的签名结果
            return {
                success: true,
                timestamp: Date.now(),
                signature: {
                    url: signedRequest.url,
                    method: signedRequest.method,
                    headers: signedRequest.headers,
                    data: signedRequest.data,
                    mtgsig: signedRequest.headers.mtgsig
                },
                raw: signedRequest
            };

        } catch (error) {
            this.log('签名生成失败:', error);
            return {
                success: false,
                timestamp: Date.now(),
                error: error.message,
                stack: error.stack
            };
        }
    }

    /**
     * 批量生成签名
     * @param {Array<Object>} requestList 请求列表
     * @returns {Promise<Array<Object>>} 签名结果列表
     */
    async batchGenerateSignature(requestList) {
        if (!Array.isArray(requestList)) {
            throw new Error('requestList必须是数组');
        }

        const results = [];
        
        for (let i = 0; i < requestList.length; i++) {
            try {
                const result = await this.generateSignature(requestList[i]);
                results.push({
                    index: i,
                    ...result
                });
            } catch (error) {
                results.push({
                    index: i,
                    success: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * 验证输入参数
     * @param {Object} params 参数对象
     */
    validateParams(params) {
        if (!params || typeof params !== 'object') {
            throw new Error('参数必须是对象');
        }

        if (!params.url || typeof params.url !== 'string') {
            throw new Error('url参数必须是字符串');
        }

        if (!params.method || typeof params.method !== 'string') {
            throw new Error('method参数必须是字符串');
        }

        const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
        if (!validMethods.includes(params.method.toUpperCase())) {
            throw new Error(`method必须是以下值之一: ${validMethods.join(', ')}`);
        }
    }

    /**
     * 获取服务状态
     * @returns {Object} 服务状态信息
     */
    getStatus() {
        return {
            initialized: this.initialized,
            h5guardAvailable: !!(window.H5guard && window.H5guard.sign),
            timestamp: Date.now(),
            userAgent: navigator.userAgent,
            location: window.location.href
        };
    }

    /**
     * 日志输出
     * @param {...any} args 日志参数
     */
    log(...args) {
        if (this.debug) {
            console.log('[MeituanSignatureRPC]', ...args);
        }
    }
}

// 全局RPC接口函数
window.MeituanSignatureAPI = {
    instance: null,

    /**
     * 初始化签名服务
     * @param {Object} options 配置选项
     * @returns {Promise<boolean>} 初始化结果
     */
    async init(options = {}) {
        try {
            this.instance = new MeituanSignatureRPC(options);
            await this.instance.init();
            return true;
        } catch (error) {
            console.error('签名服务初始化失败:', error);
            return false;
        }
    },

    /**
     * 生成签名
     * @param {Object} params 签名参数
     * @returns {Promise<Object>} 签名结果
     */
    async sign(params) {
        if (!this.instance) {
            throw new Error('服务未初始化，请先调用init()');
        }
        return await this.instance.generateSignature(params);
    },

    /**
     * 批量生成签名
     * @param {Array<Object>} requestList 请求列表
     * @returns {Promise<Array<Object>>} 签名结果列表
     */
    async batchSign(requestList) {
        if (!this.instance) {
            throw new Error('服务未初始化，请先调用init()');
        }
        return await this.instance.batchGenerateSignature(requestList);
    },

    /**
     * 获取服务状态
     * @returns {Object} 服务状态
     */
    getStatus() {
        if (!this.instance) {
            return { initialized: false, error: '服务未初始化' };
        }
        return this.instance.getStatus();
    }
};

// 自动初始化（可选）
if (typeof window !== 'undefined') {
    // 等待页面加载完成后自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('美团签名RPC服务已加载，使用 MeituanSignatureAPI.init() 初始化');
        });
    } else {
        console.log('美团签名RPC服务已加载，使用 MeituanSignatureAPI.init() 初始化');
    }
}

// 导出模块（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MeituanSignatureRPC, MeituanSignatureAPI: window.MeituanSignatureAPI };
}
