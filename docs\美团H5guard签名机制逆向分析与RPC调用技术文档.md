# 美团H5guard签名机制RPC调用技术文档

## 📋 目录
1. [概述与背景](#概述与背景)
2. [明文获取方法详解](#明文获取方法详解)
3. [RPC调用实现指南](#rpc调用实现指南)
4. [使用示例](#使用示例)

---

## 概述与背景

### 项目背景
美团在其Web应用中部署了名为**H5guard**的前端安全防护系统。该系统通过签名算法生成`mtgsig`参数，用于验证API请求的合法性。

### 核心目标
1. 获取签名算法的输入明文数据
2. 实现基于H5guard.sign的RPC调用方法
3. 提供完整的使用示例

### mtgsig参数结构
```javascript
{
    "a1": "1.2",                    // H5guard版本号
    "a2": 1754105536043,            // 当前时间戳(毫秒)
    "a3": "x8uu03x4695u5v7x...",    // 设备指纹/会话标识
    "a5": "63AgdyGEl8fzFlbS...",    // Base64编码的签名数据1
    "a6": "h1.84QJECv4GQwu...",     // Base64编码的主签名数据
    "a8": "809c8ec9c5ea542f...",    // MD5哈希值
    "a9": "3.2.1,7,48",             // 版本信息字符串
    "a10": "ba",                    // 简短标识符
    "x0": 4,                        // 数字标识符
    "d1": "3081082f48cdebc6..."     // 另一个哈希值
}
```

---

## 明文获取方法详解

### 核心思路
通过JavaScript函数Hook技术，拦截H5guard内部的关键函数调用，捕获加密前的原始数据。

### 方法一：JSON.stringify Hook
```javascript
// 保存原始函数
const originalStringify = JSON.stringify;
let capturedData = [];

// Hook JSON.stringify
JSON.stringify = function(...args) {
    if (args[0] && typeof args[0] === 'object') {
        const stack = new Error().stack;

        // 只捕获来自H5guard的调用
        if (stack.includes('h5guard') || stack.includes('H5guard')) {
            capturedData.push({
                timestamp: Date.now(),
                data: args[0],
                type: 'JSON.stringify'
            });
        }
    }
    return originalStringify.apply(this, args);
};
```

### 方法二：H5guard.sign Hook
```javascript
// Hook H5guard.sign方法
if (window.H5guard && window.H5guard.sign) {
    const originalSign = window.H5guard.sign;

    window.H5guard.sign = function(params) {
        console.log('H5guard.sign输入参数:', params);

        const result = originalSign.call(this, params);

        if (result && typeof result.then === 'function') {
            return result.then(signedRequest => {
                console.log('签名完成:', {
                    input: params,
                    output: signedRequest
                });
                return signedRequest;
            });
        }

        return result;
    };
}
```

### 捕获到的明文数据结构

#### 1. 请求基本信息
```javascript
{
    method: "POST",
    url: "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    data: '{"mobile":"17139144117","countrycode":"86"}'
}
```

#### 2. 元数据数组
```javascript
[
    1754104236500,  // 基准时间戳
    5,              // 版本标识
    "3.2.1",        // H5guard版本
    1,              // 标志位
    1754104236456,  // 另一个时间戳
    0, 0, 1, 0      // 其他标志位
]
```

---

## RPC调用实现指南

### 直接使用H5guard.sign（推荐方法）

#### 优势
- 简单易用，无需逆向复杂算法
- 兼容性好，跟随官方更新
- 成功率高，签名完全正确

#### 实现步骤

1. **环境准备**
```javascript
// 确保在美团页面环境中执行
// 等待H5guard加载的函数
function waitForH5guard(timeout = 10000) {
    return new Promise((resolve, reject) => {
        if (window.H5guard) {
            resolve(window.H5guard);
            return;
        }

        const startTime = Date.now();
        const checkInterval = setInterval(() => {
            if (window.H5guard) {
                clearInterval(checkInterval);
                resolve(window.H5guard);
            } else if (Date.now() - startTime > timeout) {
                clearInterval(checkInterval);
                reject(new Error('H5guard加载超时'));
            }
        }, 100);
    });
}

// 使用示例
waitForH5guard().then(() => {
    console.log('H5guard已加载，可以开始使用');
    // 在这里执行您的代码
}).catch(error => {
    console.error('H5guard加载失败:', error);
});
```

2. **签名生成**
```javascript
async function generateSignature(url, method, data) {
    try {
        const signedRequest = await window.H5guard.sign({
            url: url,
            method: method,
            data: data
        });

        return signedRequest;
    } catch (error) {
        console.error('签名生成失败:', error);
        throw error;
    }
}
```

3. **API调用**
```javascript
async function callMeituanAPI(url, method, data, additionalHeaders = {}) {
    // 第一步：生成签名
    const signedRequest = await generateSignature(url, method, data);

    // 第二步：构造请求头
    const headers = {
        'Content-Type': 'application/json;charset=UTF-8',
        'mtgsig': signedRequest.headers.mtgsig,
        'User-Agent': navigator.userAgent,
        'Referer': window.location.href,
        'Origin': window.location.origin,
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        ...additionalHeaders
    };

    // 第三步：发送请求
    const response = await fetch(signedRequest.url, {
        method: signedRequest.method,
        headers: headers,
        body: signedRequest.method !== 'GET' ?
              JSON.stringify(signedRequest.data) : undefined,
        credentials: 'include',
        mode: 'cors'
    });

    // 第四步：处理响应
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    return {
        success: true,
        status: response.status,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries())
    };
}
```

---

## 使用示例

### 通用RPC调用类
```javascript
class MeituanAPIClient {
    constructor(options = {}) {
        this.baseURL = options.baseURL || 'https://sqt.meituan.com';
        this.timeout = options.timeout || 30000;
        this.debug = options.debug || false;

        // 检查环境
        this.checkEnvironment();
    }

    checkEnvironment() {
        if (typeof window === 'undefined') {
            throw new Error('必须在浏览器环境中运行');
        }

        if (!window.H5guard) {
            throw new Error('H5guard未加载，请在美团页面中执行');
        }
    }

    async call(endpoint, method = 'GET', data = null, options = {}) {
        const url = endpoint.startsWith('http') ? endpoint : `${this.baseURL}${endpoint}`;

        try {
            // 生成签名
            const signedRequest = await this.generateSignature(url, method, data);

            // 构造请求配置
            const requestConfig = this.buildRequestConfig(signedRequest, options);

            // 发送请求
            const response = await this.sendRequest(requestConfig);

            return this.handleResponse(response);

        } catch (error) {
            return this.handleError(error);
        }
    }

    async generateSignature(url, method, data) {
        try {
            const signedRequest = await window.H5guard.sign({
                url: url,
                method: method.toUpperCase(),
                data: data
            });

            if (this.debug) {
                console.log('[DEBUG] 签名结果:', signedRequest);
            }

            return signedRequest;

        } catch (error) {
            console.error('签名生成失败:', error);
            throw new Error(`签名生成失败: ${error.message}`);
        }
    }

    buildRequestConfig(signedRequest, options) {
        const headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'mtgsig': signedRequest.headers.mtgsig,
            'User-Agent': navigator.userAgent,
            'Referer': window.location.href,
            'Origin': window.location.origin,
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            ...options.headers
        };

        return {
            url: signedRequest.url,
            method: signedRequest.method,
            headers: headers,
            body: signedRequest.method !== 'GET' ?
                  JSON.stringify(signedRequest.data) : undefined,
            credentials: 'include',
            mode: 'cors',
            signal: options.signal
        };
    }

    async sendRequest(config) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(config.url, {
                ...config,
                signal: config.signal || controller.signal
            });

            clearTimeout(timeoutId);
            return response;

        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    async handleResponse(response) {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        let data;

        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        return {
            success: true,
            status: response.status,
            statusText: response.statusText,
            data: data,
            headers: Object.fromEntries(response.headers.entries())
        };
    }

    handleError(error) {
        console.error('API调用失败:', error);

        return {
            success: false,
            error: error.message,
            stack: error.stack
        };
    }

    // 便捷方法
    async get(endpoint, options = {}) {
        return this.call(endpoint, 'GET', null, options);
    }

    async post(endpoint, data, options = {}) {
        return this.call(endpoint, 'POST', data, options);
    }

    async put(endpoint, data, options = {}) {
        return this.call(endpoint, 'PUT', data, options);
    }

    async delete(endpoint, options = {}) {
        return this.call(endpoint, 'DELETE', null, options);
    }
}
```

### 使用示例
```javascript
// 创建API客户端
const client = new MeituanAPIClient({
    debug: true,
    timeout: 10000
});

// 发送验证码
async function sendSMS(mobile, countrycode = '86') {
    try {
        const result = await client.post('/s/gateway/login/h5/login/sendLoginFreeSmsCode', {
            mobile: mobile,
            countrycode: countrycode
        });
        
        console.log('发送验证码结果:', result);
        return result;
        
    } catch (error) {
        console.error('发送验证码失败:', error);
        throw error;
    }
}

// 验证验证码
async function verifySMS(mobile, code, countrycode = '86') {
    try {
        const result = await client.post('/s/gateway/login/h5/login/loginBySmsCode', {
            mobile: mobile,
            smsCode: code,
            countrycode: countrycode
        });
        
        console.log('验证码验证结果:', result);
        return result;
        
    } catch (error) {
        console.error('验证码验证失败:', error);
        throw error;
    }
}

// 使用示例
(async () => {
    try {
        // 发送验证码
        await sendSMS('17139144117');
        
        // 等待用户输入验证码
        const code = prompt('请输入验证码:');
        
        // 验证验证码
        await verifySMS('17139144117', code);
        
    } catch (error) {
        console.error('操作失败:', error);
    }
})();
```

### 快速开始

1. **在美团页面打开浏览器控制台**
2. **复制粘贴MeituanAPIClient类代码**
3. **创建客户端实例并调用API**

```javascript
// 快速测试
const client = new MeituanAPIClient({ debug: true });

// 测试发送验证码
client.post('/s/gateway/login/h5/login/sendLoginFreeSmsCode', {
    mobile: '您的手机号',
    countrycode: '86'
}).then(result => {
    console.log('成功:', result);
}).catch(error => {
    console.error('失败:', error);
});
```

---

**免责声明**：本文档仅供技术学习和研究使用，使用者应自行承担使用风险并遵守相关法律法规。

