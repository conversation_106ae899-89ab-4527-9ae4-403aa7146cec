# data 参数获取指南

## 概述

在美团签名API中，`data` 参数是指**实际发送给美团API的请求数据**。这个参数需要根据你要调用的具体美团API接口来确定。

## data 参数的作用

`data` 参数用于：
1. **签名计算** - H5guard会根据这些数据计算签名
2. **API调用** - 这些数据会作为实际的请求体发送给美团服务器
3. **参数验证** - 美团服务器会验证签名与数据的一致性

## 如何获取 data 参数

### 方法1：查看美团官方API文档（推荐）

如果你有美团开放平台的API文档，直接查看对应接口的参数说明。

### 方法2：浏览器开发者工具抓包

1. **打开美团网站**
   ```
   https://sqt.meituan.com
   ```

2. **打开开发者工具**
   - 按 F12 或右键选择"检查"
   - 切换到 "Network" 标签页

3. **执行目标操作**
   - 比如发送验证码、登录等操作

4. **查看网络请求**
   - 找到对应的API请求
   - 查看 "Request Payload" 或 "Form Data"

### 方法3：使用我们的测试用例

项目中已经包含了一些常用的测试用例，可以直接参考：

## 常用API的 data 参数示例

### 1. 发送登录验证码

**API接口：** `/s/gateway/login/h5/login/sendLoginFreeSmsCode`

**data 参数：**
```json
{
  "mobile": "17139144117",
  "countrycode": "86"
}
```

**完整请求示例：**
```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {
      "mobile": "17139144117",
      "countrycode": "86"
    }
  }'
```

### 2. 短信验证码登录

**API接口：** `/s/gateway/login/h5/login/loginBySmsCode`

**data 参数：**
```json
{
  "mobile": "17139144117",
  "smsCode": "123456",
  "countrycode": "86"
}
```

### 3. GET请求（无data参数）

**API接口：** `/api/user/profile`

**data 参数：**
```json
null
```
或者不传 data 字段

### 4. 商品搜索

**API接口：** `/api/search/goods`

**data 参数：**
```json
{
  "keyword": "火锅",
  "cityId": "1",
  "page": 1,
  "limit": 20
}
```

## 实际获取步骤演示

### 步骤1：打开美团网站并准备抓包

1. 打开 Chrome 浏览器
2. 访问 `https://sqt.meituan.com`
3. 按 F12 打开开发者工具
4. 切换到 "Network" 标签
5. 勾选 "Preserve log" 保持日志

### 步骤2：执行目标操作

以发送验证码为例：
1. 点击登录按钮
2. 选择手机号登录
3. 输入手机号
4. 点击"发送验证码"

### 步骤3：查看网络请求

1. 在 Network 面板中找到 `sendLoginFreeSmsCode` 请求
2. 点击该请求查看详情
3. 在 "Headers" 标签下找到 "Request Payload"
4. 复制其中的JSON数据

**示例抓包结果：**
```
Request URL: https://sqt.meituan.com/s/gateway/login/h5/login/sendLoginFreeSmsCode
Request Method: POST
Request Payload:
{
  "mobile": "17139144117",
  "countrycode": "86"
}
```

### 步骤4：提取data参数

从抓包结果中提取 Request Payload 部分作为 data 参数：

```json
{
  "mobile": "17139144117",
  "countrycode": "86"
}
```

## 注意事项

### 1. 数据格式
- **JSON格式**：大多数API使用JSON格式
- **表单格式**：少数API可能使用表单格式
- **空数据**：GET请求通常不需要data参数

### 2. 参数验证
- 手机号格式要正确
- 必填参数不能缺少
- 参数类型要匹配（字符串、数字等）

### 3. 测试建议
- 先用真实数据测试签名生成
- 确认签名格式正确后再用于实际业务
- 注意保护用户隐私数据

## 常见错误及解决方案

### 错误1：参数缺失
```json
{
  "success": false,
  "error": "缺少必要参数"
}
```

**解决方案：** 检查API文档，确保所有必填参数都已包含

### 错误2：参数格式错误
```json
{
  "success": false,
  "error": "参数格式不正确"
}
```

**解决方案：** 检查参数类型，确保字符串、数字格式正确

### 错误3：手机号格式错误
```json
{
  "success": false,
  "error": "手机号格式不正确"
}
```

**解决方案：** 使用正确的11位手机号格式

## 进阶用法

### 动态参数生成

```python
import time

def generate_login_data(mobile):
    """生成登录验证码请求数据"""
    return {
        "mobile": mobile,
        "countrycode": "86",
        "timestamp": int(time.time() * 1000)  # 某些API需要时间戳
    }

# 使用示例
data = generate_login_data("17139144117")
```

### 批量测试不同参数

```python
test_cases = [
    {
        "name": "发送验证码",
        "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
        "method": "POST",
        "data": {"mobile": "17139144117", "countrycode": "86"}
    },
    {
        "name": "商品搜索",
        "url": "/api/search/goods",
        "method": "POST",
        "data": {"keyword": "火锅", "cityId": "1"}
    }
]
```

## 总结

`data` 参数的获取方法：

1. **查看API文档**（最准确）
2. **浏览器抓包**（最常用）
3. **参考测试用例**（最快速）
4. **逆向分析**（最复杂）

建议优先使用前三种方法，它们更简单、更可靠。
