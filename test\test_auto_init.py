#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试美团签名服务自动初始化功能

此脚本用于测试服务启动后的自动初始化功能。

使用方法:
1. 启动服务: python meituan_signature_server.py
2. 运行测试: python test/test_auto_init.py

作者: AI Assistant
日期: 2025-01-02
"""

import time
import requests
import json

def test_auto_initialization():
    """测试自动初始化功能"""
    base_url = "http://localhost:5000"
    
    print("=" * 50)
    print("测试美团签名服务自动初始化功能")
    print("=" * 50)
    
    # 等待服务启动
    print("1. 等待服务启动...")
    time.sleep(2)
    
    # 检查服务是否可访问
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✓ 服务已启动")
        else:
            print(f"✗ 服务启动异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务: {e}")
        print("请确保服务已启动: python meituan_signature_server.py")
        return False
    
    # 等待自动初始化完成
    print("2. 等待自动初始化完成...")
    max_wait_time = 30  # 最大等待30秒
    wait_interval = 2   # 每2秒检查一次
    
    for i in range(0, max_wait_time, wait_interval):
        try:
            response = requests.get(f"{base_url}/api/signature/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                if status.get('initialized'):
                    print(f"✓ 自动初始化成功 (耗时: {i + wait_interval}秒)")
                    print(f"  状态详情: {json.dumps(status, ensure_ascii=False, indent=2)}")
                    return True
                else:
                    print(f"  等待中... ({i + wait_interval}秒)")
            else:
                print(f"  状态检查失败，状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"  状态检查异常: {e}")
        
        time.sleep(wait_interval)
    
    print(f"✗ 自动初始化超时 (超过{max_wait_time}秒)")
    
    # 显示最终状态
    try:
        response = requests.get(f"{base_url}/api/signature/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"  最终状态: {json.dumps(status, ensure_ascii=False, indent=2)}")
    except:
        pass
    
    return False

def test_manual_initialization():
    """测试手动初始化功能（作为备用方案）"""
    base_url = "http://localhost:5000"
    
    print("\n3. 测试手动初始化功能...")
    
    try:
        response = requests.post(f"{base_url}/api/signature/init", timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 手动初始化成功")
                return True
            else:
                print(f"✗ 手动初始化失败: {result.get('error', '未知错误')}")
        else:
            print(f"✗ 手动初始化请求失败，状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ 手动初始化异常: {e}")
    
    return False

def test_signature_generation():
    """测试签名生成功能"""
    base_url = "http://localhost:5000"
    
    print("\n4. 测试签名生成功能...")
    
    test_data = {
        "url": "https://sqt.meituan.com/api/test",
        "method": "POST",
        "data": {"test": "data"}
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/signature/sign",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 签名生成成功")
                print(f"  签名结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return True
            else:
                print(f"✗ 签名生成失败: {result.get('error', '未知错误')}")
        else:
            print(f"✗ 签名生成请求失败，状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ 签名生成异常: {e}")
    
    return False

if __name__ == '__main__':
    print("开始测试美团签名服务...")
    
    # 测试自动初始化
    auto_init_success = test_auto_initialization()
    
    # 如果自动初始化失败，尝试手动初始化
    if not auto_init_success:
        manual_init_success = test_manual_initialization()
        if not manual_init_success:
            print("\n测试结果: 初始化失败，无法继续测试")
            exit(1)
    
    # 测试签名生成
    sign_success = test_signature_generation()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  自动初始化: {'✓ 成功' if auto_init_success else '✗ 失败'}")
    print(f"  签名生成: {'✓ 成功' if sign_success else '✗ 失败'}")
    print("=" * 50)
    
    if auto_init_success and sign_success:
        print("🎉 所有测试通过！服务运行正常。")
    else:
        print("⚠️  部分测试失败，请检查服务状态。")
