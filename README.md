# 美团H5guard签名API服务

基于DrissionPage的美团H5guard签名生成服务，提供HTTP API接口供其他程序调用。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install flask DrissionPage
```

### 2. 启动服务
```bash
python meituan_signature_server.py
```

### 3. 初始化服务
```bash
curl -X POST http://localhost:5000/api/signature/init
```

### 4. 生成签名
```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {"mobile": "17139144117", "countrycode": "86"}
  }'
```

## 📁 项目结构

```
├── README.md                           # 项目说明
├── TASK.md                            # 任务记录
├── meituan_signature_rpc.js           # 签名RPC核心脚本
├── meituan_signature_server.py        # HTTP API服务器
├── docs/                              # 文档目录
│   ├── drissionpage_usage_guide.md    # 使用指南
│   └── 美团H5guard签名机制逆向分析与RPC调用技术文档.md
└── test/                              # 测试目录
    ├── test_drissionpage_direct.py    # 直接功能测试
    └── test_http_api_simple.py        # HTTP API测试
```

## 🔧 API接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/` | 服务信息 |
| POST | `/api/signature/init` | 初始化签名服务 |
| POST | `/api/signature/sign` | 生成签名 |
| GET | `/api/signature/status` | 获取服务状态 |

## 🧪 测试

```bash
# 直接测试DrissionPage功能
python test/test_drissionpage_direct.py

# 测试HTTP API
python test/test_http_api_simple.py
```

## ✨ 特性

- ✅ **无需ChromeDriver** - 使用DrissionPage自动管理浏览器
- ✅ **简单易用** - 提供REST API接口
- ✅ **性能优秀** - 低资源占用，高效签名生成
- ✅ **完整测试** - 包含直接测试和API测试
- ✅ **详细文档** - 完整的使用指南和技术文档

## 📖 详细文档

- [DrissionPage使用指南](docs/drissionpage_usage_guide.md)
- [美团H5guard技术文档](docs/美团H5guard签名机制逆向分析与RPC调用技术文档.md)
- [任务记录](TASK.md)

## ⚠️ 注意事项

本项目仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目仅供学习研究使用。
