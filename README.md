# 美团H5guard签名API服务

基于DrissionPage的美团H5guard签名生成服务，提供HTTP API接口供其他程序调用。

## ✨ 新特性

🎉 **v1.1.0 - 自动初始化功能**
- 🚀 **零配置启动** - 服务启动后自动初始化，无需手动操作
- ⚡ **后台初始化** - 不阻塞服务启动，提升用户体验
- 📊 **状态监控** - 实时查看初始化进度和服务状态
- 🔄 **智能重试** - 初始化失败时提供备用方案

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install flask DrissionPage
```

### 2. 启动服务（自动初始化）
```bash
python meituan_signature_server.py
```

启动后会看到：
```
==================================================
美团签名API服务启动中... (DrissionPage版)
API文档: http://localhost:5000
==================================================
正在自动初始化签名服务...
✓ 签名服务自动初始化成功
```

### 3. 直接使用签名服务
无需手动初始化，等待自动初始化完成后即可使用：

```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {"mobile": "17139144117", "countrycode": "86"}
  }'
```

### 4. 检查服务状态
```bash
curl http://localhost:5000/api/signature/status
```

## 📁 项目结构

```
├── README.md                           # 项目说明
├── TASK.md                            # 任务记录
├── meituan_signature_rpc.js           # 签名RPC核心脚本
├── meituan_signature_server.py        # HTTP API服务器（支持自动初始化）
├── docs/                              # 文档目录
│   ├── auto_init_usage.md             # 自动初始化使用指南
│   ├── data_parameter_guide.md        # data参数获取指南
│   ├── drissionpage_usage_guide.md    # DrissionPage使用指南
│   └── 美团H5guard签名机制逆向分析与RPC调用技术文档.md
└── test/                              # 测试目录
    ├── test_auto_init.py              # 自动初始化测试
    ├── test_data_examples.py          # data参数示例测试
    ├── test_signature_format.py       # 签名格式测试
    ├── test_drissionpage_direct.py    # 直接功能测试
    └── test_http_api_simple.py        # HTTP API测试
```

## 🔧 API接口

| 方法 | 路径 | 说明 | 状态 |
|------|------|------|------|
| GET | `/` | 服务信息 | ✅ |
| POST | `/api/signature/init` | 手动初始化签名服务（可选） | ✅ |
| POST | `/api/signature/sign` | 生成签名 | ✅ |
| GET | `/api/signature/status` | 获取服务状态 | ✅ |

### API使用示例

#### 获取服务信息
```bash
curl http://localhost:5000/
```

#### 检查服务状态
```bash
curl http://localhost:5000/api/signature/status
```

响应示例：
```json
{
  "initialized": true,
  "browser_active": true,
  "service_status": {
    "ready": true,
    "version": "1.0.0"
  }
}
```

#### 生成签名
```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {"mobile": "17139144117", "countrycode": "86"}
  }'
```

#### 手动初始化（备用方案）
```bash
curl -X POST http://localhost:5000/api/signature/init
```

## 📊 data 参数说明

### 什么是 data 参数？

`data` 参数是指**发送给美团API的实际请求数据**。不同的API接口需要不同的参数格式。

### 常用 data 参数示例

#### 1. 发送验证码
```json
{
  "mobile": "17139144117",
  "countrycode": "86"
}
```

#### 2. 验证码登录
```json
{
  "mobile": "17139144117",
  "smsCode": "123456",
  "countrycode": "86"
}
```

#### 3. GET请求（无需data）
```json
null
```

### 如何获取 data 参数？

1. **查看API文档**（推荐）
2. **浏览器抓包**：
   - 打开 `https://sqt.meituan.com`
   - 按F12打开开发者工具
   - 执行操作，查看Network面板中的Request Payload
3. **参考测试示例**：
   ```bash
   python test/test_data_examples.py
   ```

详细说明请查看：[📊 data参数获取指南](docs/data_parameter_guide.md)

### 📋 返回格式优化

v1.1.0 优化了签名API的返回格式，避免重复数据：

```json
{
  "success": true,
  "timestamp": 1754117451193,
  "request": {
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": { "mobile": "17139144117", "mobileInterCode": "86" }
  },
  "signature": {
    "mtgsig": "{\"a1\":\"1.2\",\"a2\":1754117451191,...}"
  }
}
```

**使用签名：** 将 `signature.mtgsig` 添加到请求头的 `mtgsig` 字段中。

## 🧪 测试

### 自动初始化测试（推荐）
```bash
# 启动服务
python meituan_signature_server.py

# 在另一个终端运行完整测试
python test/test_auto_init.py
```

### 其他测试
```bash
# 测试签名返回格式（验证格式优化）
python test/test_signature_format.py

# 测试data参数示例（了解如何构造请求数据）
python test/test_data_examples.py

# 直接测试DrissionPage功能
python test/test_drissionpage_direct.py

# 测试HTTP API
python test/test_http_api_simple.py
```

### 测试结果示例
```
==================================================
测试美团签名服务自动初始化功能
==================================================
1. 等待服务启动...
✓ 服务已启动
2. 等待自动初始化完成...
✓ 自动初始化成功 (耗时: 12秒)
4. 测试签名生成功能...
✓ 签名生成成功

==================================================
测试总结:
  自动初始化: ✓ 成功
  签名生成: ✓ 成功
==================================================
🎉 所有测试通过！服务运行正常。
```

## ✨ 特性

### 🚀 核心特性
- ✅ **自动初始化** - 服务启动后自动初始化，零配置使用
- ✅ **无需ChromeDriver** - 使用DrissionPage自动管理浏览器
- ✅ **简单易用** - 提供REST API接口，支持多种HTTP方法
- ✅ **性能优秀** - 低资源占用，高效签名生成
- ✅ **智能重试** - 初始化失败时提供备用方案

### 🔧 技术特性
- ✅ **后台初始化** - 不阻塞服务启动，提升用户体验
- ✅ **状态监控** - 实时查看初始化进度和服务状态
- ✅ **线程安全** - 支持并发请求，保证数据一致性
- ✅ **异常处理** - 完善的错误处理和资源清理机制
- ✅ **完整测试** - 包含自动初始化、直接测试和API测试

### 📚 文档特性
- ✅ **详细文档** - 完整的使用指南和技术文档
- ✅ **故障排除** - 常见问题解决方案
- ✅ **最佳实践** - 生产环境部署建议

## 📖 详细文档

### 📋 使用指南
- [🚀 自动初始化使用指南](docs/auto_init_usage.md) - **推荐阅读**
- [📊 data参数获取指南](docs/data_parameter_guide.md) - **重要参考**
- [DrissionPage使用指南](docs/drissionpage_usage_guide.md)
- [任务记录](TASK.md)

### 📚 技术文档
- [美团H5guard技术文档](docs/美团H5guard签名机制逆向分析与RPC调用技术文档.md)

## 🔧 故障排除

### 常见问题

#### 1. 自动初始化失败
**现象：** 看到 `✗ 签名服务自动初始化失败` 错误

**解决方案：**
```bash
# 方案1: 手动重新初始化
curl -X POST http://localhost:5000/api/signature/init

# 方案2: 检查服务状态
curl http://localhost:5000/api/signature/status

# 方案3: 重启服务
# Ctrl+C 停止服务，然后重新启动
python meituan_signature_server.py
```

#### 2. 浏览器初始化失败
**现象：** `浏览器初始化失败` 错误

**解决方案：**
- 确保已安装Chrome浏览器
- 检查系统权限和防火墙设置
- 尝试以管理员权限运行

#### 3. 网络连接问题
**现象：** `H5guard未正确加载` 错误

**解决方案：**
- 检查网络连接
- 确保能访问 `https://sqt.meituan.com`
- 检查代理设置

### 获取帮助
如果遇到其他问题，请：
1. 查看详细错误日志
2. 运行测试脚本诊断问题
3. 查阅相关文档
4. 提交Issue描述问题

## 🚀 版本历史

### v1.1.0 (2025-01-02) - 自动初始化版本
- ✨ **新增自动初始化功能** - 服务启动后自动初始化
- 🚀 **优化启动流程** - 后台初始化，不阻塞服务启动
- 📊 **增强状态监控** - 实时查看初始化进度
- 🔧 **改进错误处理** - 详细的错误信息和备用方案
- 📝 **完善文档** - 新增使用指南和故障排除
- 🧪 **增加测试** - 自动初始化功能测试脚本

### v1.0.0 (2025-01-02) - 基础版本
- 🎉 **项目初始发布**
- 🔧 **基于DrissionPage** - 无需ChromeDriver的HTTP服务器
- 📚 **完整文档** - API文档和使用指南
- 🧪 **测试覆盖** - 直接测试和HTTP API测试

## 🎯 路线图

### 计划中的功能
- [ ] **配置文件支持** - 支持自定义配置文件
- [ ] **多实例支持** - 支持多个签名服务实例
- [ ] **缓存机制** - 签名结果缓存优化
- [ ] **监控面板** - Web界面监控服务状态
- [ ] **Docker支持** - 容器化部署方案

## ⚠️ 注意事项

本项目仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

### 贡献指南
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目仅供学习研究使用。

---

**⭐ 如果这个项目对您有帮助，请给个Star支持一下！**
