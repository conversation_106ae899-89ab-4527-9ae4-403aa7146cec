#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试签名返回格式优化

此脚本用于测试修复后的签名API返回格式，
确保没有重复数据，结构清晰。

使用方法:
1. 启动签名服务: python meituan_signature_server.py
2. 运行此脚本: python test/test_signature_format.py

作者: AI Assistant
日期: 2025-01-02
"""

import requests
import json
import time
from typing import Dict, Any

class SignatureFormatTester:
    """签名格式测试器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        
    def test_signature_format(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """测试签名格式"""
        try:
            print(f"\n🔐 测试签名生成...")
            print(f"📤 请求数据:")
            print(json.dumps(test_data, ensure_ascii=False, indent=2))
            
            response = requests.post(
                f"{self.base_url}/api/signature/sign",
                json=test_data,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"\n📥 响应数据:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                # 分析返回结构
                self.analyze_response_structure(result)
                
                return {
                    "success": True,
                    "response": result,
                    "analysis": self.get_structure_analysis(result)
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ 请求失败: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 测试异常: {error_msg}")
            return {
                "success": False,
                "error": error_msg
            }
    
    def analyze_response_structure(self, result: Dict[str, Any]):
        """分析响应结构"""
        print(f"\n📊 结构分析:")
        
        # 检查顶级字段
        top_fields = list(result.keys())
        print(f"✅ 顶级字段: {top_fields}")
        
        # 检查是否有重复数据
        duplicates = self.find_duplicates(result)
        if duplicates:
            print(f"⚠️  发现重复数据:")
            for dup in duplicates:
                print(f"   - {dup}")
        else:
            print(f"✅ 无重复数据")
        
        # 检查签名字段
        if 'signature' in result:
            sig_data = result['signature']
            if isinstance(sig_data, dict):
                sig_fields = list(sig_data.keys())
                print(f"✅ 签名字段: {sig_fields}")
                
                if 'mtgsig' in sig_data:
                    mtgsig = sig_data['mtgsig']
                    if mtgsig:
                        print(f"✅ mtgsig长度: {len(mtgsig)} 字符")
                        print(f"✅ mtgsig前缀: {mtgsig[:30]}...")
                    else:
                        print(f"❌ mtgsig为空")
                else:
                    print(f"❌ 缺少mtgsig字段")
        
        # 检查请求信息
        if 'request' in result:
            req_data = result['request']
            if isinstance(req_data, dict):
                req_fields = list(req_data.keys())
                print(f"✅ 请求字段: {req_fields}")
        
        # 检查时间戳
        if 'timestamp' in result:
            timestamp = result['timestamp']
            current_time = int(time.time() * 1000)
            time_diff = abs(current_time - timestamp)
            print(f"✅ 时间戳: {timestamp} (差异: {time_diff}ms)")
    
    def find_duplicates(self, data: Any, path: str = "", seen: Dict[str, str] = None) -> list:
        """查找重复数据"""
        if seen is None:
            seen = {}
        
        duplicates = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                
                # 将值转换为字符串进行比较
                value_str = json.dumps(value, sort_keys=True) if not isinstance(value, str) else value
                
                # 检查是否已经见过这个值
                if value_str in seen:
                    duplicates.append(f"{current_path} 与 {seen[value_str]} 重复")
                else:
                    seen[value_str] = current_path
                
                # 递归检查嵌套结构
                if isinstance(value, (dict, list)):
                    duplicates.extend(self.find_duplicates(value, current_path, seen))
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]"
                duplicates.extend(self.find_duplicates(item, current_path, seen))
        
        return duplicates
    
    def get_structure_analysis(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """获取结构分析结果"""
        analysis = {
            "top_level_fields": list(result.keys()),
            "has_duplicates": len(self.find_duplicates(result)) > 0,
            "has_signature": 'signature' in result,
            "has_mtgsig": False,
            "has_request_info": 'request' in result,
            "has_timestamp": 'timestamp' in result
        }
        
        if analysis["has_signature"]:
            sig_data = result.get('signature', {})
            analysis["has_mtgsig"] = 'mtgsig' in sig_data and bool(sig_data.get('mtgsig'))
        
        return analysis
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 签名返回格式测试")
        print("="*60)
        
        # 检查服务状态
        try:
            response = requests.get(f"{self.base_url}/api/signature/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                if not status.get('initialized'):
                    print("⚠️  签名服务未初始化，请等待自动初始化完成")
                    return
                else:
                    print("✅ 签名服务已就绪")
            else:
                print("❌ 无法连接到签名服务")
                return
        except:
            print("❌ 无法连接到签名服务，请确保服务已启动")
            return
        
        # 测试用例
        test_cases = [
            {
                "name": "真实美团API参数",
                "data": {
                    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode?yodaReady=h5&csecplatform=4&csecversion=3.2.1",
                    "method": "POST",
                    "data": {
                        "sqtTk": "81669ed3423a4c72a7ba1ba498f82efd",
                        "uuid": "591B468A904E7FC6D621D8DF8444472648BC6D096ABDA4E6EDDEAB340FAEDD7E",
                        "mobile": "17139144117",
                        "mobileInterCode": "86"
                    }
                }
            },
            {
                "name": "简化参数",
                "data": {
                    "url": "/api/test",
                    "method": "POST",
                    "data": {
                        "test": "data"
                    }
                }
            },
            {
                "name": "GET请求",
                "data": {
                    "url": "/api/user/profile",
                    "method": "GET",
                    "data": None
                }
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"📋 测试用例 {i}: {test_case['name']}")
            print(f"{'='*60}")
            
            result = self.test_signature_format(test_case['data'])
            results.append({
                "test_case": test_case['name'],
                "result": result
            })
            
            if i < len(test_cases):
                print(f"\n⏳ 等待3秒后继续下一个测试...")
                time.sleep(3)
        
        # 总结
        print(f"\n{'='*60}")
        print(f"📊 测试总结")
        print(f"{'='*60}")
        
        success_count = sum(1 for r in results if r['result']['success'])
        total_count = len(results)
        
        print(f"总测试数: {total_count}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 分析结构改进
        print(f"\n📈 结构改进分析:")
        for result in results:
            if result['result']['success']:
                analysis = result['result']['analysis']
                print(f"\n✅ {result['test_case']}:")
                print(f"   - 顶级字段: {analysis['top_level_fields']}")
                print(f"   - 有重复数据: {'是' if analysis['has_duplicates'] else '否'}")
                print(f"   - 包含签名: {'是' if analysis['has_signature'] else '否'}")
                print(f"   - 包含mtgsig: {'是' if analysis['has_mtgsig'] else '否'}")
                print(f"   - 包含请求信息: {'是' if analysis['has_request_info'] else '否'}")
                print(f"   - 包含时间戳: {'是' if analysis['has_timestamp'] else '否'}")
        
        if success_count == total_count:
            print(f"\n🎉 所有测试通过！返回格式已优化。")
        else:
            print(f"\n⚠️  部分测试失败，请检查服务状态。")

def main():
    """主函数"""
    tester = SignatureFormatTester()
    
    print("选择测试模式:")
    print("1. 运行综合测试")
    print("2. 测试特定参数")
    
    try:
        choice = input("\n请输入选择 (1-2): ").strip()
        
        if choice == "1":
            tester.run_comprehensive_test()
            
        elif choice == "2":
            print("\n请输入测试参数 (JSON格式):")
            print("示例: {\"url\": \"/api/test\", \"method\": \"POST\", \"data\": {\"test\": \"data\"}}")
            
            try:
                user_input = input("\n参数: ").strip()
                test_data = json.loads(user_input)
                tester.test_signature_format(test_data)
            except json.JSONDecodeError:
                print("❌ JSON格式错误")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                
        else:
            print("❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")

if __name__ == '__main__':
    main()
