#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美团API data参数示例测试工具

此脚本演示了不同美团API接口的data参数格式，
帮助用户理解如何构造正确的请求数据。

使用方法:
1. 启动签名服务: python meituan_signature_server.py
2. 运行此脚本: python test/test_data_examples.py

作者: AI Assistant
日期: 2025-01-02
"""

import requests
import json
import time
from typing import Dict, Any, List

class MeituanDataExamples:
    """美团API data参数示例"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        
    def get_data_examples(self) -> List[Dict[str, Any]]:
        """获取各种API的data参数示例"""
        return [
            {
                "name": "发送登录验证码",
                "description": "向指定手机号发送登录验证码",
                "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
                "method": "POST",
                "data": {
                    "mobile": "17139144117",      # 手机号（必填）
                    "countrycode": "86"           # 国家代码（必填）
                },
                "required_fields": ["mobile", "countrycode"],
                "optional_fields": [],
                "notes": "手机号必须是11位数字，国家代码默认86（中国）"
            },
            {
                "name": "短信验证码登录",
                "description": "使用手机号和验证码进行登录",
                "url": "/s/gateway/login/h5/login/loginBySmsCode",
                "method": "POST",
                "data": {
                    "mobile": "17139144117",      # 手机号（必填）
                    "smsCode": "123456",          # 验证码（必填）
                    "countrycode": "86"           # 国家代码（必填）
                },
                "required_fields": ["mobile", "smsCode", "countrycode"],
                "optional_fields": [],
                "notes": "验证码通常是6位数字，需要先调用发送验证码接口"
            },
            {
                "name": "用户信息查询",
                "description": "获取当前用户的基本信息",
                "url": "/api/user/profile",
                "method": "GET",
                "data": None,  # GET请求通常不需要data
                "required_fields": [],
                "optional_fields": [],
                "notes": "GET请求通常不需要data参数，或者data为null"
            },
            {
                "name": "商品搜索",
                "description": "根据关键词搜索商品",
                "url": "/api/search/goods",
                "method": "POST",
                "data": {
                    "keyword": "火锅",            # 搜索关键词（必填）
                    "cityId": "1",               # 城市ID（必填）
                    "page": 1,                   # 页码（可选，默认1）
                    "limit": 20                  # 每页数量（可选，默认20）
                },
                "required_fields": ["keyword", "cityId"],
                "optional_fields": ["page", "limit"],
                "notes": "cityId需要根据实际城市获取，keyword支持中文"
            },
            {
                "name": "订单查询",
                "description": "查询用户的订单列表",
                "url": "/api/order/list",
                "method": "POST",
                "data": {
                    "status": "all",             # 订单状态（可选）
                    "page": 1,                   # 页码（可选）
                    "pageSize": 10,              # 每页数量（可选）
                    "startTime": "2025-01-01",   # 开始时间（可选）
                    "endTime": "2025-01-31"      # 结束时间（可选）
                },
                "required_fields": [],
                "optional_fields": ["status", "page", "pageSize", "startTime", "endTime"],
                "notes": "所有参数都是可选的，status可选值：all, pending, completed, cancelled"
            },
            {
                "name": "地址管理",
                "description": "添加新的收货地址",
                "url": "/api/address/add",
                "method": "POST",
                "data": {
                    "name": "张三",              # 收货人姓名（必填）
                    "phone": "17139144117",      # 收货人电话（必填）
                    "province": "北京市",         # 省份（必填）
                    "city": "北京市",            # 城市（必填）
                    "district": "朝阳区",        # 区县（必填）
                    "address": "某某街道123号",   # 详细地址（必填）
                    "isDefault": True            # 是否默认地址（可选）
                },
                "required_fields": ["name", "phone", "province", "city", "district", "address"],
                "optional_fields": ["isDefault"],
                "notes": "电话号码格式需要正确，地址信息需要真实有效"
            }
        ]
    
    def test_signature_with_example(self, example: Dict[str, Any]) -> Dict[str, Any]:
        """使用示例数据测试签名生成"""
        try:
            request_data = {
                "url": example["url"],
                "method": example["method"],
                "data": example["data"]
            }
            
            response = requests.post(
                f"{self.base_url}/api/signature/sign",
                json=request_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "signature": result.get("signature"),
                    "response": result
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}",
                    "response": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def print_example_details(self, example: Dict[str, Any]):
        """打印示例的详细信息"""
        print(f"\n{'='*60}")
        print(f"📋 {example['name']}")
        print(f"{'='*60}")
        print(f"📝 描述: {example['description']}")
        print(f"🔗 接口: {example['url']}")
        print(f"📤 方法: {example['method']}")
        
        if example['data']:
            print(f"📊 数据参数:")
            print(json.dumps(example['data'], ensure_ascii=False, indent=2))
        else:
            print(f"📊 数据参数: 无（GET请求）")
        
        if example['required_fields']:
            print(f"✅ 必填字段: {', '.join(example['required_fields'])}")
        
        if example['optional_fields']:
            print(f"🔧 可选字段: {', '.join(example['optional_fields'])}")
        
        print(f"💡 说明: {example['notes']}")
    
    def run_all_examples(self):
        """运行所有示例"""
        print("🚀 美团API data参数示例测试")
        print("="*60)
        
        # 检查服务状态
        try:
            response = requests.get(f"{self.base_url}/api/signature/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                if not status.get('initialized'):
                    print("⚠️  签名服务未初始化，请等待自动初始化完成")
                    return
            else:
                print("❌ 无法连接到签名服务，请确保服务已启动")
                return
        except:
            print("❌ 无法连接到签名服务，请确保服务已启动")
            print("   启动命令: python meituan_signature_server.py")
            return
        
        examples = self.get_data_examples()
        success_count = 0
        
        for i, example in enumerate(examples, 1):
            self.print_example_details(example)
            
            print(f"\n🔐 测试签名生成...")
            result = self.test_signature_with_example(example)
            
            if result['success']:
                print(f"✅ 签名生成成功")
                signature = result.get('signature', {})
                if signature and signature.get('mtgsig'):
                    print(f"   签名长度: {len(signature['mtgsig'])} 字符")
                    print(f"   签名前缀: {signature['mtgsig'][:20]}...")
                success_count += 1
            else:
                print(f"❌ 签名生成失败: {result['error']}")
            
            if i < len(examples):
                print(f"\n⏳ 等待2秒后继续下一个测试...")
                time.sleep(2)
        
        print(f"\n{'='*60}")
        print(f"📊 测试总结")
        print(f"{'='*60}")
        print(f"总测试数: {len(examples)}")
        print(f"成功数量: {success_count}")
        print(f"失败数量: {len(examples) - success_count}")
        print(f"成功率: {success_count/len(examples)*100:.1f}%")
        
        if success_count == len(examples):
            print(f"\n🎉 所有测试通过！data参数格式都正确。")
        else:
            print(f"\n⚠️  部分测试失败，请检查服务状态或网络连接。")

def main():
    """主函数"""
    tester = MeituanDataExamples()
    
    print("选择测试模式:")
    print("1. 查看所有data参数示例")
    print("2. 运行签名生成测试")
    print("3. 查看特定示例详情")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            examples = tester.get_data_examples()
            for example in examples:
                tester.print_example_details(example)
                
        elif choice == "2":
            tester.run_all_examples()
            
        elif choice == "3":
            examples = tester.get_data_examples()
            print("\n可用示例:")
            for i, example in enumerate(examples, 1):
                print(f"{i}. {example['name']}")
            
            try:
                index = int(input("\n请选择示例编号: ")) - 1
                if 0 <= index < len(examples):
                    tester.print_example_details(examples[index])
                else:
                    print("❌ 无效的示例编号")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        else:
            print("❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 测试已取消")

if __name__ == '__main__':
    main()
