# 美团签名API DrissionPage版本使用指南

## 概述

本指南介绍如何使用DrissionPage版本的美团H5guard签名API服务。相比Selenium版本，DrissionPage版本具有以下优势：

- ✅ **无需ChromeDriver** - 自动管理浏览器驱动
- ✅ **安装简单** - 只需 `pip install DrissionPage`
- ✅ **性能更好** - 更低的资源占用
- ✅ **API简洁** - 更易于使用和维护

## 快速开始

### 1. 安装依赖

```bash
pip install flask DrissionPage
```

### 2. 启动服务器

```bash
python meituan_signature_server.py
```

服务器启动后会显示：
```
==================================================
美团签名API服务启动中... (DrissionPage版)
API文档: http://localhost:5000
==================================================
```

### 3. 初始化服务

```bash
curl -X POST http://localhost:5000/api/signature/init
```

响应示例：
```json
{
  "success": true,
  "message": "签名服务初始化成功"
}
```

### 4. 生成签名

```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {
      "mobile": "17139144117",
      "countrycode": "86"
    }
  }'
```

响应示例：
```json
{
  "success": true,
  "timestamp": 1754116414644,
  "signature": {
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "headers": {
      "mtgsig": "{\"a1\":\"1.2\",\"a2\":1754116414644,\"a3\":\"1754116281455...\"}"
    },
    "data": {
      "mobile": "17139144117",
      "countrycode": "86"
    },
    "mtgsig": "{\"a1\":\"1.2\",\"a2\":1754116414644,\"a3\":\"1754116281455...\"}"
  }
}
```

## API接口详情

### GET /

获取服务信息

**响应：**
```json
{
  "service": "美团签名API服务 - DrissionPage版",
  "version": "1.0.0",
  "endpoints": {
    "init": "POST /api/signature/init",
    "sign": "POST /api/signature/sign", 
    "status": "GET /api/signature/status"
  }
}
```

### POST /api/signature/init

初始化签名服务

**响应：**
```json
{
  "success": true,
  "message": "签名服务初始化成功"
}
```

### POST /api/signature/sign

生成签名

**请求体：**
```json
{
  "url": "string",     // 请求URL
  "method": "string",  // HTTP方法 (GET/POST/PUT/DELETE/PATCH)
  "data": "object"     // 请求数据 (可选)
}
```

**响应：**
```json
{
  "success": true,
  "timestamp": 1754116414644,
  "signature": {
    "url": "string",
    "method": "string", 
    "headers": {
      "mtgsig": "string"
    },
    "data": "object",
    "mtgsig": "string"
  }
}
```

### GET /api/signature/status

获取服务状态

**响应：**
```json
{
  "initialized": true,
  "browser_active": true,
  "service_status": {
    "initialized": true,
    "h5guardAvailable": true,
    "timestamp": 1754116414644,
    "userAgent": "Mozilla/5.0...",
    "location": "https://sqt.meituan.com/#/user/login"
  }
}
```

## Python客户端示例

```python
import requests
import json

class MeituanSignatureClient:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def init_service(self):
        """初始化服务"""
        response = self.session.post(f"{self.base_url}/api/signature/init")
        return response.json()
    
    def generate_signature(self, url, method, data=None):
        """生成签名"""
        payload = {
            "url": url,
            "method": method,
            "data": data
        }
        response = self.session.post(
            f"{self.base_url}/api/signature/sign",
            json=payload
        )
        return response.json()
    
    def get_status(self):
        """获取状态"""
        response = self.session.get(f"{self.base_url}/api/signature/status")
        return response.json()

# 使用示例
client = MeituanSignatureClient()

# 初始化服务
init_result = client.init_service()
print("初始化结果:", init_result)

# 生成签名
signature_result = client.generate_signature(
    url="/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    method="POST",
    data={"mobile": "17139144117", "countrycode": "86"}
)
print("签名结果:", signature_result)

# 获取mtgsig用于API调用
if signature_result.get('success'):
    mtgsig = signature_result['signature']['mtgsig']
    print("mtgsig:", mtgsig)
```

## 常见问题

### Q: 服务初始化失败怎么办？
A: 检查以下几点：
1. 确保网络连接正常，能访问美团页面
2. 确保DrissionPage正确安装
3. 检查是否有防火墙阻止浏览器启动
4. 查看服务器日志获取详细错误信息

### Q: 签名生成失败怎么办？
A: 检查以下几点：
1. 确保服务已正确初始化
2. 检查请求参数格式是否正确
3. 确保美团页面H5guard正常加载
4. 重新初始化服务

### Q: 如何在生产环境使用？
A: 建议：
1. 使用进程管理器（如supervisor）管理服务
2. 配置反向代理（如nginx）
3. 添加认证和限流机制
4. 监控服务状态和性能

## 测试

运行测试脚本验证功能：

```bash
# 直接测试DrissionPage功能
python test/test_drissionpage_direct.py

# 测试HTTP API
python test/test_http_api_simple.py
```

## 技术支持

如有问题，请检查：
1. 项目文档和示例代码
2. DrissionPage官方文档
3. 美团H5guard相关技术文档

---

**注意：** 本服务仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。
