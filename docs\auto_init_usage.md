# 美团签名服务自动初始化使用指南

## 概述

美团签名服务现已支持自动初始化功能。服务启动后会自动在后台进行初始化，无需手动调用初始化接口。

## 功能特性

### 🚀 自动初始化
- **无需手动操作**：服务启动后自动执行初始化
- **后台执行**：初始化在后台线程进行，不阻塞服务启动
- **状态反馈**：提供详细的初始化状态和错误信息
- **备用方案**：保留手动初始化接口作为备用

### 📊 状态监控
- **实时状态**：通过 `/api/signature/status` 接口查看初始化进度
- **错误处理**：初始化失败时提供详细错误信息
- **重试机制**：支持手动重新初始化

## 使用方法

### 1. 启动服务

```bash
python meituan_signature_server.py
```

启动后会看到类似输出：
```
==================================================
美团签名API服务启动中... (DrissionPage版)
API文档: http://localhost:5000
==================================================
正在自动初始化签名服务...
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
Press CTRL+C to quit
✓ 签名服务自动初始化成功
```

### 2. 检查初始化状态

```bash
curl http://localhost:5000/api/signature/status
```

成功响应示例：
```json
{
  "initialized": true,
  "browser_active": true,
  "service_status": {
    "ready": true,
    "version": "1.0.0"
  }
}
```

### 3. 直接使用签名服务

初始化完成后，可直接调用签名接口：

```bash
curl -X POST http://localhost:5000/api/signature/sign \
  -H "Content-Type: application/json" \
  -d '{
    "url": "/s/gateway/login/h5/login/sendLoginFreeSmsCode",
    "method": "POST",
    "data": {"mobile": "17139144117", "countrycode": "86"}
  }'
```

## 故障排除

### 自动初始化失败

如果看到以下错误信息：
```
✗ 签名服务自动初始化失败: H5guard未正确加载
  您可以稍后手动调用 POST /api/signature/init 进行初始化
```

**解决方案：**

1. **手动重新初始化**
   ```bash
   curl -X POST http://localhost:5000/api/signature/init
   ```

2. **检查网络连接**
   确保能够访问美团网站：`https://sqt.meituan.com`

3. **检查浏览器环境**
   确保系统已安装Chrome浏览器

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|---------|------|---------|
| `浏览器初始化失败` | Chrome浏览器未安装或路径错误 | 安装Chrome浏览器 |
| `H5guard未正确加载` | 网络问题或美团页面变更 | 检查网络连接，稍后重试 |
| `RPC脚本文件未找到` | 缺少`meituan_signature_rpc.js`文件 | 确保文件存在于项目根目录 |
| `签名API初始化失败` | JavaScript执行错误 | 查看详细错误信息，检查脚本兼容性 |

## 测试工具

### 自动化测试脚本

运行完整的自动初始化测试：

```bash
python test/test_auto_init.py
```

测试脚本会：
1. 检查服务连接状态
2. 监控自动初始化进度
3. 测试手动初始化备用方案
4. 验证签名生成功能
5. 提供详细测试报告

### 手动测试步骤

1. **启动服务**
   ```bash
   python meituan_signature_server.py
   ```

2. **等待初始化**（通常需要10-20秒）

3. **检查状态**
   ```bash
   curl http://localhost:5000/api/signature/status
   ```

4. **测试签名**
   ```bash
   curl -X POST http://localhost:5000/api/signature/sign \
     -H "Content-Type: application/json" \
     -d '{"url": "/test", "method": "GET"}'
   ```

## 性能优化

### 初始化时间优化

- **并行启动**：服务启动和初始化并行进行
- **缓存机制**：浏览器实例复用，减少重复初始化
- **超时控制**：合理的超时设置，避免长时间等待

### 资源管理

- **内存优化**：及时清理不用的浏览器资源
- **线程安全**：使用线程锁保证并发安全
- **异常处理**：完善的异常处理和资源清理

## 最佳实践

1. **生产环境部署**
   - 使用进程管理器（如PM2、Supervisor）
   - 配置健康检查和自动重启
   - 监控初始化状态和服务可用性

2. **开发环境使用**
   - 启用调试模式查看详细日志
   - 使用测试脚本验证功能
   - 定期更新依赖和脚本

3. **错误处理**
   - 监控初始化失败率
   - 设置重试机制
   - 记录详细错误日志

## 更新日志

### v1.1.0 (2025-01-02)
- ✨ 新增自动初始化功能
- 🚀 优化启动流程，提升用户体验
- 🔧 增加详细的状态反馈
- 📝 完善文档和测试脚本

### v1.0.0 (2025-01-02)
- 🎉 初始版本发布
- 🔧 基于DrissionPage的HTTP服务器
- 📚 完整的API文档和使用指南
